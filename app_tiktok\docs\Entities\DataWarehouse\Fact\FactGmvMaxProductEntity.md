# FactGmvMaxProductEntity

## Thông tin bảng dữ liệu

-   **Table**: `Fact_GmvMaxProduct`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_GmvMaxProduct`
-   **DbSchema**: `null`

## Mô tả

Fact table cho GMV Max Product Detail - Chi tiết hiệu suất từng sản phẩm trong GMV Max Campaign. Phục vụ drill-down analysis từ cấp độ Campaign xuống Product level với các metrics: Revenue, Orders, CostPerOrder, ROAS, TACOS theo từng sản phẩm. Đ<PERSON><PERSON>c xây dựng từ dữ liệu thô: RawGmvMaxProductDetailProductReportEntity, RawGmvMaxProductCreativeReportEntity.

## Bảng dữ liệu: Fact_GmvMaxProduct

| Tên Field                  | Kiểu dữ liệu | <PERSON><PERSON><PERSON> buộ<PERSON> | <PERSON><PERSON> dài | <PERSON>ô tả                                                                                                                       |
| -------------------------- | ------------ | -------- | ------ | --------------------------------------------------------------------------------------------------------------------------- |
| Id                         | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                                                       |
| DimDateId                  | int          | ✅       | -      | Foreign Key đến Dim_Date                                                                                                    |
| DimBusinessCenterId        | Guid         | ✅       | -      | Foreign Key đến Dim_BusinessCenter                                                                                          |
| DimAdAccountId             | Guid         | ✅       | -      | Foreign Key đến Dim_AdAccount                                                                                               |
| DimCampaignId              | Guid         | ✅       | -      | Foreign Key đến Dim_Campaign                                                                                                |
| DimStoreId                 | Guid         | ✅       | -      | Foreign Key đến Dim_Store (TikTok Shop)                                                                                     |
| DimProductId               | Guid         | ✅       | -      | Foreign Key đến Dim_Product                                                                                                 |
| CampaignId                 | string       | ✅       | 100    | Business Key - Campaign ID từ TikTok                                                                                        |
| StoreId                    | string       | ✅       | 100    | Business Key - Store ID từ TikTok Shop                                                                                      |
| ProductId                  | string       | ✅       | 100    | Business Key - Product ID từ TikTok Shop                                                                                    |
| BcId                       | string       | ✅       | 100    | Business Key - Business Center ID từ TikTok                                                                                 |
| AdvertiserId               | string       | ✅       | 100    | Business Key - Advertiser ID từ TikTok                                                                                      |
| ItemId                     | string       | ✅       | 100    | Business Key - ItemId từ TikTok                                                                                             |
| ProductName                | string       | ❌       | 500    | Tên sản phẩm                                                                                                                |
| ProductImageUrl            | string       | ❌       | 1000   | URL hình ảnh sản phẩm                                                                                                       |
| ProductStatus              | string       | ❌       | 20     | Trạng thái sản phẩm (available, unavailable)                                                                                |
| CreativeType               | string       | ❌       | 20     | Loại creative (ADS_AND_ORGANIC, ORGANIC, REMOVED)                                                                           |
| TtAccountName              | string       | ❌       | 200    | Tên TikTok account                                                                                                          |
| TtAccountProfileImageUrl   | string       | ❌       | 1000   | URL hình đại diện TikTok account                                                                                            |
| TtAccountAuthorizationType | string       | ❌       | 20     | Loại ủy quyền (TTS_TT, AFFILIATE, TT_USER, BC_AUTH_TT, AUTH_CODE, UNSET)                                                    |
| ShopContentType            | string       | ❌       | 20     | Loại nội dung shop (VIDEO, PRODUCT_CARD)                                                                                    |
| Orders                     | int          | ✅       | -      | Số lượng đơn hàng (Orders)                                                                                                  |
| CostPerOrder               | decimal?     | ❌       | -      | Chi phí trung bình mỗi đơn hàng (CPO)                                                                                       |
| CostPerOrderVND            | decimal?     | ❌       | -      | Chi phí trung bình mỗi đơn hàng (CPO) (VND)                                                                                 |
| CostPerOrderUSD            | decimal?     | ❌       | -      | Chi phí trung bình mỗi đơn hàng (CPO) (USD)                                                                                 |
| GrossRevenue               | decimal      | ✅       | -      | Tổng doanh thu (Gross Revenue)                                                                                              |
| GrossRevenueVND            | decimal?     | ❌       | -      | Tổng doanh thu (Gross Revenue) (VND)                                                                                        |
| GrossRevenueUSD            | decimal?     | ❌       | -      | Tổng doanh thu (Gross Revenue) (USD)                                                                                        |
| ProductImpressions         | long?        | ❌       | -      | Tổng lượt hiển thị sản phẩm (organic + paid)                                                                                |
| ProductClicks              | long?        | ❌       | -      | Tổng lượt click sản phẩm (organic + paid)                                                                                   |
| ProductClickRate           | decimal?     | ❌       | -      | Tỷ lệ click sản phẩm (ProductClicks/ProductImpressions)                                                                     |
| AdClickRate                | decimal?     | ❌       | -      | Tỷ lệ click-through của paid views từ video này                                                                             |
| AdConversionRate           | decimal?     | ❌       | -      | Tỷ lệ chuyển đổi của paid clicks từ video này                                                                               |
| AdVideoViewRate2s          | decimal?     | ❌       | -      | Tỷ lệ xem video ít nhất 2 giây                                                                                              |
| AdVideoViewRate6s          | decimal?     | ❌       | -      | Tỷ lệ xem video ít nhất 6 giây                                                                                              |
| AdVideoViewRateP25         | decimal?     | ❌       | -      | Tỷ lệ xem video ít nhất 25% thời lượng                                                                                      |
| AdVideoViewRateP50         | decimal?     | ❌       | -      | Tỷ lệ xem video ít nhất 50% thời lượng                                                                                      |
| AdVideoViewRateP75         | decimal?     | ❌       | -      | Tỷ lệ xem video ít nhất 75% thời lượng                                                                                      |
| AdVideoViewRateP100        | decimal?     | ❌       | -      | Tỷ lệ xem video 100% thời lượng                                                                                             |
| ROAS                       | decimal?     | ❌       | -      | ROAS (Return on Ad Spend) - Hiệu quả quảng cáo                                                                              |
| TACOS                      | decimal?     | ❌       | -      | True ACOS (TACOS) - Hiệu quả quảng cáo so với tổng doanh thu                                                                |
| Currency                   | string       | ✅       | 10     | Tiền tệ                                                                                                                     |
| Date                       | DateTime     | ✅       | -      | Ngày báo cáo UTC format yyyy-MM-dd 00:00:00 (theo ngày)                                                                     |
| Cost                       | decimal      | ✅       | -      | Tổng chi phí                                                                                                                |
| CreativeDeliveryStatus     | enum         | ✅       | -      | Trạng thái creative (IN_QUEUE, LEARNING, DELIVERING, NOT_DELIVERYIN, AUTHORIZATION_NEEDED, EXCLUDED, UNAVAILABLE, REJECTED) |
| CreationTime               | DateTime     | ✅       | -      | Thời gian tạo bản ghi (AuditedEntity)                                                                                       |
| CreatorId                  | Guid?        | ❌       | -      | ID người tạo (AuditedEntity)                                                                                                |
| LastModificationTime       | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (AuditedEntity)                                                                                      |
| LastModifierId             | Guid?        | ❌       | -      | ID người sửa đổi cuối (AuditedEntity)                                                                                       |

## Cấu trúc dữ liệu

### Foreign Keys

-   **DimDateId**: Liên kết với Dim_Date để phân tích theo thời gian
-   **DimBusinessCenterId**: Liên kết với Dim_BusinessCenter để phân tích theo tổ chức
-   **DimAdAccountId**: Liên kết với Dim_AdAccount để phân tích theo tài khoản
-   **DimCampaignId**: Liên kết với Dim_Campaign để phân tích theo chiến dịch
-   **DimStoreId**: Liên kết với Dim_Store để phân tích theo TikTok Shop
-   **DimProductId**: Liên kết với Dim_Product để phân tích theo sản phẩm

### Business Keys

-   **CampaignId**: ID chiến dịch từ TikTok API
-   **StoreId**: ID cửa hàng từ TikTok Shop
-   **ProductId**: ID sản phẩm từ TikTok Shop
-   **BcId**: ID Business Center từ TikTok
-   **AdvertiserId**: ID nhà quảng cáo từ TikTok
-   **ItemId**: ItemId từ TikTok

### Metrics chính cho Product Analysis

#### 1. Product Performance Overview

-   **Total Product Revenue**: SUM(GrossRevenue) theo sản phẩm
-   **Average Product ROAS**: AVG(ROAS) theo sản phẩm
-   **Product Orders**: SUM(Orders) theo sản phẩm
-   **Average Product CPO**: AVG(CostPerOrder) theo sản phẩm
-   **Product Impressions**: SUM(ProductImpressions) theo sản phẩm
-   **Product Clicks**: SUM(ProductClicks) theo sản phẩm
-   **Total Product Cost**: SUM(Cost) theo sản phẩm
-   **Product Creative Status**: Phân tích theo CreativeDeliveryStatus

#### 2. Product Drill-down Analysis

-   **Product vs Campaign Performance**: So sánh hiệu suất sản phẩm trong từng chiến dịch
-   **Product vs Store Performance**: So sánh hiệu suất sản phẩm theo cửa hàng
-   **Product Creative Analysis**: Phân tích hiệu suất theo loại creative (VIDEO, PRODUCT_CARD)
-   **Product TikTok Account Analysis**: Phân tích hiệu suất theo TikTok account

#### 3. Product Conversion Metrics

-   **Product Conversion Rate**: Orders / ProductClicks
-   **Product Cost Per Order**: CostPerOrder (từ raw data)
-   **Product Revenue Per Order**: GrossRevenue / Orders
-   **Product Click Rate**: ProductClicks / ProductImpressions

#### 4. Product Comparison Analysis

-   **Product vs Product**: So sánh hiệu suất giữa các sản phẩm
-   **Product Performance Ranking**: Xếp hạng sản phẩm theo ROAS/TACOS
-   **Product Trend Analysis**: Phân tích xu hướng hiệu suất sản phẩm theo thời gian

### Công thức tính toán

#### ROAS (Return on Ad Spend)

```
ROAS = GrossRevenue / Cost
```

#### TACOS (True ACOS)

```
TACOS = Cost / GrossRevenue
```

#### Cost (Tổng chi phí)

```
Cost = Tổng chi phí từ RawGmvMaxProductCreativeReportEntity.Cost
```

#### CPO (Cost Per Order)

```
CPO = CostPerOrder (từ RawGmvMaxProductCampaignReportEntity.CostPerOrder, RawGmvMaxProductCreativeReportEntity.CostPerOrder)
```

#### Product Click Rate

```
ProductClickRate = ProductClicks / ProductImpressions
```

#### Product Conversion Rate

```
ProductConversionRate = Orders / ProductClicks
```

## Navigation Properties

-   **Date**: Liên kết với Dim_DateEntity
-   **BusinessCenter**: Liên kết với Dim_BusinessCenterEntity
-   **AdAccount**: Liên kết với Dim_AdAccountEntity
-   **Campaign**: Liên kết với Dim_CampaignEntity
-   **Store**: Liên kết với Dim_StoreEntity
-   **Product**: Liên kết với Dim_ProductEntity

## Mục đích sử dụng

-   Drill-down analysis từ Campaign xuống Product level
-   Phân tích hiệu suất từng sản phẩm trong GMV Max
-   So sánh hiệu suất giữa các sản phẩm
-   Phân tích ROAS và TACOS theo từng sản phẩm
-   Quản lý hiệu suất sản phẩm theo chiến dịch
-   Tối ưu hóa sản phẩm trong GMV Max Campaign
-   Phân tích hiệu suất creative (video/product card)
-   Phân tích hiệu suất theo TikTok account
-   Hỗ trợ ra quyết định về sản phẩm

## Quan hệ với các entity khác

-   **Dim_DateEntity**: Many-to-One, phân tích theo thời gian
-   **Dim_BusinessCenterEntity**: Many-to-One, phân tích theo tổ chức
-   **Dim_AdAccountEntity**: Many-to-One, phân tích theo tài khoản
-   **Dim_CampaignEntity**: Many-to-One, phân tích theo chiến dịch
-   **Dim_StoreEntity**: Many-to-One, phân tích theo TikTok Shop
-   **Dim_ProductEntity**: Many-to-One, phân tích theo sản phẩm
-   **FactGmvMaxCampaignEntity**: One-to-Many, drill-up từ product lên campaign

## Use Cases thực tế

### Product Performance Analysis

```sql
-- Phân tích hiệu suất theo sản phẩm
SELECT
    f.ProductName,
    f.ProductId,
    f.ItemId,
    f.ProductStatus,
    f.CreativeType,
    f.ShopContentType,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    AVG(f.CostPerOrder) as AverageCPO,
    SUM(f.ProductImpressions) as TotalImpressions,
    SUM(f.ProductClicks) as TotalClicks
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY f.ProductName, f.ProductId, f.ItemId, f.ProductStatus, f.CreativeType, f.ShopContentType
ORDER BY TotalRevenue DESC
```

### Product vs Campaign Analysis

```sql
-- Phân tích hiệu suất sản phẩm theo chiến dịch
SELECT
    c.CampaignName,
    f.ProductName,
    f.CreativeType,
    f.ShopContentType,
    SUM(f.GrossRevenue) as ProductRevenue,
    AVG(f.ROAS) as ProductROAS,
    AVG(f.TACOS) as ProductTACOS,
    SUM(f.Orders) as ProductOrders,
    AVG(f.CostPerOrder) as ProductCPO,
    SUM(f.ProductImpressions) as ProductImpressions,
    SUM(f.ProductClicks) as ProductClicks
FROM Fact_GmvMaxProduct f
JOIN Dim_Campaign c ON f.DimCampaignId = c.Id
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY c.CampaignName, f.ProductName, f.CreativeType, f.ShopContentType
ORDER BY ProductRevenue DESC
```

### Product Creative Performance

```sql
-- Phân tích hiệu suất theo loại creative
SELECT
    f.CreativeType,
    f.ShopContentType,
    COUNT(DISTINCT f.ProductId) as ProductCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    AVG(f.CostPerOrder) as AverageCPO,
    SUM(f.ProductImpressions) as TotalImpressions,
    SUM(f.ProductClicks) as TotalClicks
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY f.CreativeType, f.ShopContentType
ORDER BY TotalRevenue DESC
```

### Product TikTok Account Performance

```sql
-- Phân tích hiệu suất theo TikTok account
SELECT
    f.TtAccountName,
    f.TtAccountAuthorizationType,
    COUNT(DISTINCT f.ProductId) as ProductCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    AVG(f.CostPerOrder) as AverageCPO,
    SUM(f.ProductImpressions) as TotalImpressions,
    SUM(f.ProductClicks) as TotalClicks
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
  AND f.TtAccountName IS NOT NULL
GROUP BY f.TtAccountName, f.TtAccountAuthorizationType
ORDER BY TotalRevenue DESC
```

### Product Conversion Analysis

```sql
-- Phân tích tỷ lệ chuyển đổi theo sản phẩm
SELECT
    f.ProductName,
    f.CreativeType,
    f.ShopContentType,
    SUM(f.Orders) as TotalOrders,
    SUM(f.ProductClicks) as TotalClicks,
    SUM(f.ProductImpressions) as TotalImpressions,
    SUM(f.Orders) / NULLIF(SUM(f.ProductClicks), 0) as ConversionRate,
    SUM(f.ProductClicks) / NULLIF(SUM(f.ProductImpressions), 0) as ClickRate,
    AVG(f.CostPerOrder) as AverageCPO
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY f.ProductName, f.CreativeType, f.ShopContentType
HAVING SUM(f.Orders) > 0
ORDER BY ConversionRate DESC
```

### Product Trend Analysis

```sql
-- Phân tích xu hướng hiệu suất sản phẩm theo thời gian
SELECT
    f.Date,
    f.ProductName,
    f.CreativeType,
    SUM(f.GrossRevenue) as DailyRevenue,
    AVG(f.ROAS) as DailyROAS,
    AVG(f.TACOS) as DailyTACOS,
    SUM(f.Orders) as DailyOrders,
    AVG(f.CostPerOrder) as DailyCPO,
    SUM(f.ProductImpressions) as DailyImpressions,
    SUM(f.ProductClicks) as DailyClicks
FROM Fact_GmvMaxProduct f
WHERE f.Date >= @Last7Days
GROUP BY f.Date, f.ProductName, f.CreativeType
ORDER BY f.Date, DailyRevenue DESC
```

### Product Store Performance

```sql
-- Phân tích hiệu suất sản phẩm theo cửa hàng
SELECT
    s.StoreName,
    f.ProductName,
    f.CreativeType,
    SUM(f.GrossRevenue) as ProductRevenue,
    AVG(f.ROAS) as ProductROAS,
    AVG(f.TACOS) as ProductTACOS,
    SUM(f.Orders) as ProductOrders,
    AVG(f.CostPerOrder) as ProductCPO,
    SUM(f.ProductImpressions) as ProductImpressions,
    SUM(f.ProductClicks) as ProductClicks
FROM Fact_GmvMaxProduct f
JOIN Dim_Store s ON f.DimStoreId = s.Id
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY s.StoreName, f.ProductName, f.CreativeType
ORDER BY ProductRevenue DESC
```

### Product Video Performance Analysis

```sql
-- Phân tích hiệu suất video theo sản phẩm
SELECT
    f.ProductName,
    f.CreativeType,
    f.ShopContentType,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    AVG(f.AdVideoViewRate2s) as AvgVideoViewRate2s,
    AVG(f.AdVideoViewRate6s) as AvgVideoViewRate6s,
    AVG(f.AdVideoViewRateP25) as AvgVideoViewRateP25,
    AVG(f.AdVideoViewRateP50) as AvgVideoViewRateP50,
    AVG(f.AdVideoViewRateP75) as AvgVideoViewRateP75,
    AVG(f.AdVideoViewRateP100) as AvgVideoViewRateP100
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
  AND f.ShopContentType = 'VIDEO'
GROUP BY f.ProductName, f.CreativeType, f.ShopContentType
ORDER BY TotalRevenue DESC
```

### Product Creative Delivery Status Analysis

```sql
-- Phân tích hiệu suất theo trạng thái creative delivery
SELECT
    f.CreativeDeliveryStatus,
    COUNT(DISTINCT f.ProductId) as ProductCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    SUM(f.Cost) as TotalCost,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    AVG(f.CostPerOrder) as AverageCPO,
    SUM(f.ProductImpressions) as TotalImpressions,
    SUM(f.ProductClicks) as TotalClicks
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY f.CreativeDeliveryStatus
ORDER BY TotalRevenue DESC
```

### Product Cost Analysis

```sql
-- Phân tích chi phí theo sản phẩm
SELECT
    f.ProductName,
    f.ProductId,
    f.ItemId,
    f.CreativeType,
    f.CreativeDeliveryStatus,
    SUM(f.Cost) as TotalCost,
    SUM(f.GrossRevenue) as TotalRevenue,
    SUM(f.Cost) / NULLIF(SUM(f.GrossRevenue), 0) as CostRatio,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    SUM(f.Cost) / NULLIF(SUM(f.Orders), 0) as CostPerOrder
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY f.ProductName, f.ProductId, f.ItemId, f.CreativeType, f.CreativeDeliveryStatus
ORDER BY TotalCost DESC
```

### Product ItemId Analysis

```sql
-- Phân tích hiệu suất theo ItemId
SELECT
    f.ItemId,
    f.ProductId,
    f.ProductName,
    f.CreativeType,
    f.CreativeDeliveryStatus,
    COUNT(*) as RecordCount,
    SUM(f.Cost) as TotalCost,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROAS) as AverageROAS,
    AVG(f.TACOS) as AverageTACOS,
    SUM(f.Orders) as TotalOrders,
    AVG(f.CostPerOrder) as AverageCPO,
    SUM(f.ProductImpressions) as TotalImpressions,
    SUM(f.ProductClicks) as TotalClicks
FROM Fact_GmvMaxProduct f
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
GROUP BY f.ItemId, f.ProductId, f.ProductName, f.CreativeType, f.CreativeDeliveryStatus
ORDER BY TotalRevenue DESC
```
